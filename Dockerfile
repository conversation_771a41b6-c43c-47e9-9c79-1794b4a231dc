# AQUESA Leak Detection System - Unified Frontend & Backend Dockerfile
FROM python:3.10-slim

# Set working directory
WORKDIR /app

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH=/app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies (includes both backend and frontend dependencies)
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir python-dotenv

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p /app/logs /app/backups /app/models

# Make the unified startup script executable
RUN chmod +x /app/start_unified.py

# Create non-root user for security
RUN useradd --create-home --shell /bin/bash aquesa && \
    chown -R aquesa:aquesa /app

# Expose both ports
EXPOSE 8000 3000

# Health check for both services
HEALTHCHECK --interval=30s --timeout=30s --start-period=15s --retries=3 \
    CMD curl -f http://localhost:8000/ && curl -f http://localhost:3000/ || exit 1

# Default command - run as aquesa user
USER aquesa
CMD ["python", "/app/start_unified.py"]
