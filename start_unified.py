#!/usr/bin/env python3
"""
Unified startup script for AQUESA Leak Detection System.
Starts both backend API and frontend server in a single container.
"""

import subprocess
import sys
import time
import os
import signal
import threading
from pathlib import Path


class UnifiedAQUESAServer:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.running = True
        
    def start_backend(self):
        """Start the FastAPI backend server"""
        print("🔧 Starting Backend API Server...")
        try:
            self.backend_process = subprocess.Popen([
                sys.executable, "-m", "uvicorn", 
                "app.main:app", 
                "--host", "0.0.0.0", 
                "--port", "8000"
            ], cwd="/app")
            print("✅ Backend API Server started on port 8000")
        except Exception as e:
            print(f"❌ Failed to start backend: {e}")
            sys.exit(1)
    
    def start_frontend(self):
        """Start the frontend server"""
        print("🌐 Starting Frontend Server...")
        try:
            frontend_dir = Path("/app/frontend")
            if not frontend_dir.exists():
                print("❌ Frontend directory not found")
                sys.exit(1)
                
            self.frontend_process = subprocess.Popen([
                sys.executable, "server.py", 
                "--port", "3000"
            ], cwd=str(frontend_dir))
            print("✅ Frontend Server started on port 3000")
        except Exception as e:
            print(f"❌ Failed to start frontend: {e}")
            sys.exit(1)
    
    def monitor_processes(self):
        """Monitor both processes and restart if needed"""
        while self.running:
            try:
                # Check backend
                if self.backend_process and self.backend_process.poll() is not None:
                    print("⚠️ Backend process died, restarting...")
                    self.start_backend()
                
                # Check frontend
                if self.frontend_process and self.frontend_process.poll() is not None:
                    print("⚠️ Frontend process died, restarting...")
                    self.start_frontend()
                
                time.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                print(f"❌ Error in process monitoring: {e}")
                time.sleep(5)
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print(f"\n🛑 Received signal {signum}, shutting down...")
        self.shutdown()
    
    def shutdown(self):
        """Gracefully shutdown both processes"""
        self.running = False
        
        print("🔄 Shutting down services...")
        
        if self.backend_process:
            print("   Stopping backend...")
            self.backend_process.terminate()
            try:
                self.backend_process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
        
        if self.frontend_process:
            print("   Stopping frontend...")
            self.frontend_process.terminate()
            try:
                self.frontend_process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                self.frontend_process.kill()
        
        print("✅ Shutdown complete")
        sys.exit(0)
    
    def run(self):
        """Main run method"""
        # Set up signal handlers
        signal.signal(signal.SIGTERM, self.signal_handler)
        signal.signal(signal.SIGINT, self.signal_handler)
        
        print("=" * 60)
        print("🚀 AQUESA Leak Detection System - Unified Server")
        print("=" * 60)
        print("Starting both backend and frontend services...")
        print()
        
        # Start both services
        self.start_backend()
        time.sleep(2)  # Give backend time to start
        self.start_frontend()
        
        print()
        print("=" * 60)
        print("✅ AQUESA System Started Successfully!")
        print("=" * 60)
        print("🔗 Backend API: http://localhost:8000")
        print("🌐 Frontend UI: http://localhost:3000")
        print("📊 API Docs: http://localhost:8000/docs")
        print("❤️ Health Check: http://localhost:8000/")
        print("=" * 60)
        print("Press Ctrl+C to stop all services")
        print()
        
        # Start monitoring in a separate thread
        monitor_thread = threading.Thread(target=self.monitor_processes, daemon=True)
        monitor_thread.start()
        
        # Keep main thread alive
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            self.shutdown()


def main():
    """Main entry point"""
    server = UnifiedAQUESAServer()
    server.run()


if __name__ == "__main__":
    main()
