# AQUESA Unified Docker Setup

This document describes the unified Docker setup for the AQUESA Leak Detection System, where both the backend API and frontend UI run in a single container.

## Overview

The unified Docker setup simplifies deployment by:
- ✅ **Single Container**: Both backend and frontend in one container
- ✅ **Simplified Management**: One container to build, run, and monitor
- ✅ **Reduced Resource Usage**: Lower memory and CPU overhead
- ✅ **Easier Networking**: No inter-container communication needed
- ✅ **Streamlined Configuration**: Single configuration source

## Architecture

```
┌─────────────────────────────────────┐
│         AQUESA Container            │
├─────────────────────────────────────┤
│  ┌─────────────┐ ┌─────────────────┐│
│  │   Backend   │ │    Frontend     ││
│  │   (FastAPI) │ │   (Python HTTP)││
│  │   Port 8000 │ │    Port 3000    ││
│  └─────────────┘ └─────────────────┘│
├─────────────────────────────────────┤
│        start_unified.py             │
│     (Process Manager)               │
└─────────────────────────────────────┘
```

## Quick Start

### Using Docker Compose (Recommended)

```bash
# Production mode
docker-compose up -d

# Development mode
docker-compose -f docker-compose.dev.yml up -d

# Stop services
docker-compose down
```

### Using Management Scripts

**Linux/Mac:**
```bash
# Build and run
./docker-unified.sh build
./docker-unified.sh run

# Development mode
./docker-unified.sh dev

# Show status
./docker-unified.sh status

# View logs
./docker-unified.sh logs

# Clean up
./docker-unified.sh clean
```

**Windows:**
```cmd
REM Build and run
docker-unified.bat build
docker-unified.bat run

REM Development mode
docker-unified.bat dev

REM Show status
docker-unified.bat status

REM View logs
docker-unified.bat logs

REM Clean up
docker-unified.bat clean
```

### Manual Docker Commands

```bash
# Build the image
docker build -t aquesa-unified:latest .

# Run the container
docker run -d \
  --name aquesa-unified \
  -p 8000:8000 \
  -p 3000:3000 \
  -e MONGO_URI="your-mongodb-uri" \
  aquesa-unified:latest

# View logs
docker logs -f aquesa-unified

# Stop and remove
docker stop aquesa-unified
docker rm aquesa-unified
```

## Configuration

### Environment Variables

The unified container supports all the same environment variables as the separate containers:

**Database Configuration:**
- `MONGO_URI` - MongoDB connection string
- `DB_NAME` - Database name (default: aquesa_management)

**Server Configuration:**
- `API_HOST` - API host (default: 0.0.0.0)
- `API_PORT` - API port (default: 8000)
- `FRONTEND_PORT` - Frontend port (default: 3000)
- `API_BASE_URL` - API base URL for frontend

**Environment Settings:**
- `ENVIRONMENT` - Environment mode (development/production)
- `DEBUG` - Enable debug mode (true/false)
- `LOG_LEVEL` - Logging level (DEBUG/INFO/WARNING/ERROR)

### Configuration Files

You can also use configuration files:
- `config.json` - JSON configuration
- `config.yaml` - YAML configuration (requires PyYAML)
- `.env` - Environment variables file

## Ports

The unified container exposes two ports:
- **8000** - Backend API server
- **3000** - Frontend web server

## Health Checks

The container includes health checks for both services:
```bash
# Check if both services are healthy
curl http://localhost:8000/ && curl http://localhost:3000/
```

## Process Management

The `start_unified.py` script manages both processes:
- Starts backend and frontend servers
- Monitors processes and restarts if they crash
- Handles graceful shutdown on SIGTERM/SIGINT
- Provides detailed logging and status information

## Development vs Production

### Development Mode
- Uses `docker-compose.dev.yml`
- Enables debug mode and verbose logging
- Mounts source code as volumes for live reloading
- Relaxed CORS settings

### Production Mode
- Uses `docker-compose.yml`
- Optimized for performance and security
- No source code mounting
- Strict CORS settings
- Health checks and restart policies

## Monitoring and Logs

### View Logs
```bash
# All logs
docker-compose logs -f

# Specific service logs
docker logs aquesa-unified

# Follow logs in real-time
docker logs -f aquesa-unified
```

### Container Status
```bash
# Check container status
docker ps --filter "name=aquesa"

# Check resource usage
docker stats aquesa-unified
```

## Troubleshooting

### Common Issues

1. **Port conflicts:**
   ```bash
   # Use different ports
   docker run -p 8080:8000 -p 3001:3000 aquesa-unified:latest
   ```

2. **Database connection issues:**
   ```bash
   # Check MongoDB URI
   docker logs aquesa-unified | grep -i mongo
   ```

3. **Frontend can't connect to API:**
   ```bash
   # Check API_BASE_URL setting
   docker exec aquesa-unified env | grep API_BASE_URL
   ```

### Debug Mode

Run with debug enabled:
```bash
docker run -e DEBUG=true -e LOG_LEVEL=DEBUG aquesa-unified:latest
```

### Access Container Shell

```bash
# Open shell in running container
docker exec -it aquesa-unified /bin/bash

# Or use the management script
./docker-unified.sh shell  # Linux/Mac
docker-unified.bat shell   # Windows
```

## Migration from Separate Containers

If you were using separate backend and frontend containers:

1. **Stop old containers:**
   ```bash
   docker stop aquesa-api aquesa-frontend
   docker rm aquesa-api aquesa-frontend
   ```

2. **Update docker-compose.yml** (already done in this setup)

3. **Start unified container:**
   ```bash
   docker-compose up -d
   ```

4. **Update any external references** to point to the unified container

## Performance Considerations

### Resource Usage
- **Memory**: ~200-400MB (vs ~300-600MB for separate containers)
- **CPU**: Similar usage, better efficiency
- **Disk**: Single image reduces storage requirements

### Scaling
- For high-load scenarios, consider running multiple unified containers
- Use a load balancer (nginx) to distribute traffic
- Monitor resource usage and scale horizontally as needed

## Security

The unified container maintains the same security practices:
- Runs as non-root user (`aquesa`)
- Minimal base image (python:3.10-slim)
- Security headers in nginx configuration
- Environment-based configuration (no hardcoded secrets)

## Backup and Recovery

### Data Backup
```bash
# Backup volumes
docker run --rm -v aquesa_logs:/data -v $(pwd):/backup alpine tar czf /backup/logs-backup.tar.gz /data
```

### Configuration Backup
```bash
# Export current configuration
docker exec aquesa-unified python -c "from app.core.config import settings; import json; print(json.dumps(settings.to_dict(), indent=2))" > config-backup.json
```

This unified Docker setup provides a simpler, more efficient way to deploy and manage the AQUESA Leak Detection System while maintaining all the functionality of the separate container approach.
