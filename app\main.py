from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api.endpoints import router as api_router
from app.core.config import settings

app = FastAPI(
    title="AQUESA Smart Water Leak Detection System",
    version="1.0.0",
    debug=settings.DEBUG
)

# Add CORS middleware
if settings.CORS_ALLOW_ALL:
    cors_origins = ["*"]
else:
    # Convert comma-separated string to list
    cors_origins = [origin.strip() for origin in settings.CORS_ORIGINS.split(',') if origin.strip()]
app.add_middleware(
    CORSMiddleware,
    allow_origins=cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(api_router, prefix="/api")

@app.get("/")
def read_root():
    return {"message": "Welcome to AQUESA Smart Water Leak Detection System"}