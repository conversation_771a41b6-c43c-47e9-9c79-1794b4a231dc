@echo off
REM AQUESA Unified Docker Management Script for Windows
REM This script helps manage the unified Docker container for AQUESA

setlocal enabledelayedexpansion

REM Default values
set "ENV_FILE=.env"
set "API_PORT=8000"
set "FRONTEND_PORT=3000"
set "COMMAND="

REM Parse command line arguments
:parse_args
if "%~1"=="" goto check_command
if "%~1"=="build" set "COMMAND=build" & shift & goto parse_args
if "%~1"=="run" set "COMMAND=run" & shift & goto parse_args
if "%~1"=="dev" set "COMMAND=dev" & shift & goto parse_args
if "%~1"=="stop" set "COMMAND=stop" & shift & goto parse_args
if "%~1"=="clean" set "COMMAND=clean" & shift & goto parse_args
if "%~1"=="logs" set "COMMAND=logs" & shift & goto parse_args
if "%~1"=="status" set "COMMAND=status" & shift & goto parse_args
if "%~1"=="shell" set "COMMAND=shell" & shift & goto parse_args
if "%~1"=="--env-file" set "ENV_FILE=%~2" & shift & shift & goto parse_args
if "%~1"=="--port-api" set "API_PORT=%~2" & shift & shift & goto parse_args
if "%~1"=="--port-frontend" set "FRONTEND_PORT=%~2" & shift & shift & goto parse_args
if "%~1"=="--help" goto show_usage
echo [ERROR] Unknown option: %~1
goto show_usage

:check_command
if "%COMMAND%"=="" (
    echo [ERROR] No command provided
    goto show_usage
)

REM Set environment variables
set API_PORT=%API_PORT%
set FRONTEND_PORT=%FRONTEND_PORT%

REM Execute command
if "%COMMAND%"=="build" goto build_image
if "%COMMAND%"=="run" goto run_production
if "%COMMAND%"=="dev" goto run_development
if "%COMMAND%"=="stop" goto stop_containers
if "%COMMAND%"=="clean" goto clean_up
if "%COMMAND%"=="logs" goto show_logs
if "%COMMAND%"=="status" goto show_status
if "%COMMAND%"=="shell" goto open_shell

echo [ERROR] Unknown command: %COMMAND%
goto show_usage

:show_usage
echo AQUESA Unified Docker Management Script
echo.
echo Usage: %~nx0 [COMMAND] [OPTIONS]
echo.
echo Commands:
echo   build       Build the unified Docker image
echo   run         Run the unified container
echo   dev         Run in development mode
echo   stop        Stop running containers
echo   clean       Clean up containers and images
echo   logs        Show container logs
echo   status      Show container status
echo   shell       Open shell in running container
echo.
echo Options:
echo   --env-file FILE    Use specific environment file (default: .env)
echo   --port-api PORT    API port (default: 8000)
echo   --port-frontend PORT Frontend port (default: 3000)
echo   --help             Show this help message
echo.
echo Examples:
echo   %~nx0 build                    # Build the unified image
echo   %~nx0 run                      # Run in production mode
echo   %~nx0 dev                      # Run in development mode
echo   %~nx0 run --port-api 8080      # Run with custom API port
echo   %~nx0 logs                     # Show logs
echo   %~nx0 clean                    # Clean up everything
goto end

:build_image
echo [INFO] Building AQUESA unified Docker image...
docker build -t aquesa-unified:latest .
if %errorlevel% equ 0 (
    echo [SUCCESS] Image built successfully!
) else (
    echo [ERROR] Failed to build image
    exit /b 1
)
goto end

:run_production
echo [INFO] Starting AQUESA in production mode...
if exist "%ENV_FILE%" (
    echo [INFO] Using environment file: %ENV_FILE%
    docker-compose --env-file "%ENV_FILE%" up -d
) else (
    echo [WARNING] Environment file %ENV_FILE% not found, using defaults
    docker-compose up -d
)
if %errorlevel% equ 0 (
    echo [SUCCESS] AQUESA started successfully!
    echo [INFO] Backend API: http://localhost:%API_PORT%
    echo [INFO] Frontend UI: http://localhost:%FRONTEND_PORT%
    echo [INFO] API Documentation: http://localhost:%API_PORT%/docs
) else (
    echo [ERROR] Failed to start AQUESA
    exit /b 1
)
goto end

:run_development
echo [INFO] Starting AQUESA in development mode...
if exist "%ENV_FILE%" (
    echo [INFO] Using environment file: %ENV_FILE%
    docker-compose --env-file "%ENV_FILE%" -f docker-compose.dev.yml up -d
) else (
    echo [WARNING] Environment file %ENV_FILE% not found, using defaults
    docker-compose -f docker-compose.dev.yml up -d
)
if %errorlevel% equ 0 (
    echo [SUCCESS] AQUESA development environment started!
    echo [INFO] Backend API: http://localhost:%API_PORT%
    echo [INFO] Frontend UI: http://localhost:%FRONTEND_PORT%
    echo [INFO] API Documentation: http://localhost:%API_PORT%/docs
) else (
    echo [ERROR] Failed to start AQUESA development environment
    exit /b 1
)
goto end

:stop_containers
echo [INFO] Stopping AQUESA containers...
docker-compose down
docker-compose -f docker-compose.dev.yml down 2>nul
echo [SUCCESS] Containers stopped!
goto end

:clean_up
echo [INFO] Cleaning up AQUESA Docker resources...
call :stop_containers
docker-compose down --volumes --remove-orphans
docker-compose -f docker-compose.dev.yml down --volumes --remove-orphans 2>nul
docker rmi aquesa-unified:latest 2>nul
docker volume prune -f
echo [SUCCESS] Cleanup completed!
goto end

:show_logs
echo [INFO] Showing AQUESA container logs...
docker ps | findstr aquesa >nul
if %errorlevel% equ 0 (
    docker-compose logs -f
) else (
    echo [ERROR] No AQUESA containers are running
    exit /b 1
)
goto end

:show_status
echo [INFO] AQUESA Container Status:
echo.
docker ps | findstr aquesa >nul
if %errorlevel% equ 0 (
    docker ps --filter "name=aquesa" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    echo.
    echo [SUCCESS] Containers are running!
    echo [INFO] Backend API: http://localhost:%API_PORT%
    echo [INFO] Frontend UI: http://localhost:%FRONTEND_PORT%
) else (
    echo [WARNING] No AQUESA containers are running
)
goto end

:open_shell
echo [INFO] Opening shell in AQUESA container...
for /f "tokens=*" %%i in ('docker ps --filter "name=aquesa" --format "{{.Names}}" 2^>nul') do set "CONTAINER_NAME=%%i"
if defined CONTAINER_NAME (
    echo [INFO] Connecting to container: !CONTAINER_NAME!
    docker exec -it "!CONTAINER_NAME!" /bin/bash
) else (
    echo [ERROR] No running AQUESA containers found
    exit /b 1
)
goto end

:end
endlocal
