#!/bin/bash

# AQUESA Unified Docker Management Script
# This script helps manage the unified Docker container for AQUESA

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "AQUESA Unified Docker Management Script"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  build       Build the unified Docker image"
    echo "  run         Run the unified container"
    echo "  dev         Run in development mode"
    echo "  stop        Stop running containers"
    echo "  clean       Clean up containers and images"
    echo "  logs        Show container logs"
    echo "  status      Show container status"
    echo "  shell       Open shell in running container"
    echo ""
    echo "Options:"
    echo "  --env-file FILE    Use specific environment file (default: .env)"
    echo "  --port-api PORT    API port (default: 8000)"
    echo "  --port-frontend PORT Frontend port (default: 3000)"
    echo "  --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 build                    # Build the unified image"
    echo "  $0 run                      # Run in production mode"
    echo "  $0 dev                      # Run in development mode"
    echo "  $0 run --port-api 8080      # Run with custom API port"
    echo "  $0 logs                     # Show logs"
    echo "  $0 clean                    # Clean up everything"
}

# Parse command line arguments
COMMAND=""
ENV_FILE=".env"
API_PORT="8000"
FRONTEND_PORT="3000"

while [[ $# -gt 0 ]]; do
    case $1 in
        build|run|dev|stop|clean|logs|status|shell)
            COMMAND="$1"
            shift
            ;;
        --env-file)
            ENV_FILE="$2"
            shift 2
            ;;
        --port-api)
            API_PORT="$2"
            shift 2
            ;;
        --port-frontend)
            FRONTEND_PORT="$2"
            shift 2
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if command is provided
if [[ -z "$COMMAND" ]]; then
    print_error "No command provided"
    show_usage
    exit 1
fi

# Set environment variables
export API_PORT
export FRONTEND_PORT

# Function to build the image
build_image() {
    print_status "Building AQUESA unified Docker image..."
    docker build -t aquesa-unified:latest .
    print_success "Image built successfully!"
}

# Function to run in production mode
run_production() {
    print_status "Starting AQUESA in production mode..."
    
    if [[ -f "$ENV_FILE" ]]; then
        print_status "Using environment file: $ENV_FILE"
        docker-compose --env-file "$ENV_FILE" up -d
    else
        print_warning "Environment file $ENV_FILE not found, using defaults"
        docker-compose up -d
    fi
    
    print_success "AQUESA started successfully!"
    print_status "Backend API: http://localhost:$API_PORT"
    print_status "Frontend UI: http://localhost:$FRONTEND_PORT"
    print_status "API Documentation: http://localhost:$API_PORT/docs"
}

# Function to run in development mode
run_development() {
    print_status "Starting AQUESA in development mode..."
    
    if [[ -f "$ENV_FILE" ]]; then
        print_status "Using environment file: $ENV_FILE"
        docker-compose --env-file "$ENV_FILE" -f docker-compose.dev.yml up -d
    else
        print_warning "Environment file $ENV_FILE not found, using defaults"
        docker-compose -f docker-compose.dev.yml up -d
    fi
    
    print_success "AQUESA development environment started!"
    print_status "Backend API: http://localhost:$API_PORT"
    print_status "Frontend UI: http://localhost:$FRONTEND_PORT"
    print_status "API Documentation: http://localhost:$API_PORT/docs"
}

# Function to stop containers
stop_containers() {
    print_status "Stopping AQUESA containers..."
    docker-compose down
    docker-compose -f docker-compose.dev.yml down 2>/dev/null || true
    print_success "Containers stopped!"
}

# Function to clean up
clean_up() {
    print_status "Cleaning up AQUESA Docker resources..."
    
    # Stop containers
    stop_containers
    
    # Remove containers
    docker-compose down --volumes --remove-orphans
    docker-compose -f docker-compose.dev.yml down --volumes --remove-orphans 2>/dev/null || true
    
    # Remove images
    docker rmi aquesa-unified:latest 2>/dev/null || true
    
    # Remove unused volumes
    docker volume prune -f
    
    print_success "Cleanup completed!"
}

# Function to show logs
show_logs() {
    print_status "Showing AQUESA container logs..."
    if docker ps | grep -q aquesa; then
        docker-compose logs -f
    else
        print_error "No AQUESA containers are running"
        exit 1
    fi
}

# Function to show status
show_status() {
    print_status "AQUESA Container Status:"
    echo ""
    
    if docker ps | grep -q aquesa; then
        docker ps --filter "name=aquesa" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        echo ""
        print_success "Containers are running!"
        print_status "Backend API: http://localhost:$API_PORT"
        print_status "Frontend UI: http://localhost:$FRONTEND_PORT"
    else
        print_warning "No AQUESA containers are running"
    fi
}

# Function to open shell
open_shell() {
    print_status "Opening shell in AQUESA container..."
    
    CONTAINER_NAME=$(docker ps --filter "name=aquesa" --format "{{.Names}}" | head -n1)
    
    if [[ -n "$CONTAINER_NAME" ]]; then
        print_status "Connecting to container: $CONTAINER_NAME"
        docker exec -it "$CONTAINER_NAME" /bin/bash
    else
        print_error "No running AQUESA containers found"
        exit 1
    fi
}

# Execute command
case $COMMAND in
    build)
        build_image
        ;;
    run)
        run_production
        ;;
    dev)
        run_development
        ;;
    stop)
        stop_containers
        ;;
    clean)
        clean_up
        ;;
    logs)
        show_logs
        ;;
    status)
        show_status
        ;;
    shell)
        open_shell
        ;;
    *)
        print_error "Unknown command: $COMMAND"
        show_usage
        exit 1
        ;;
esac
