# Docker Compose for Development Environment
version: '3.8'

services:
  # AQUESA Unified Service (Development - Backend + Frontend)
  aquesa-dev:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: aquesa-unified-dev
    ports:
      - "${API_PORT:-8000}:8000"      # Backend API
      - "${FRONTEND_PORT:-3000}:3000" # Frontend UI
    environment:
      # Database Configuration
      - MONGO_URI=${MONGO_URI:-mongodb://localhost:27017}
      - DB_NAME=${DB_NAME:-aquesa_management}

      # Server Configuration
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - FRONTEND_PORT=3000
      - API_BASE_URL=http://localhost:8000/api

      # Development Settings
      - ENVIRONMENT=development
      - DEBUG=true
      - API_RELOAD=true
      - LOG_LEVEL=DEBUG
      - VERBOSE_LOGGING=true

      # Security Settings (relaxed for development)
      - CORS_ALLOW_ALL=true
      - CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

      # Other Settings
      - USE_MOCK_DATA=${USE_MOCK_DATA:-false}
      - EMAIL_NOTIFICATIONS=false
    volumes:
      - .:/app
      - ./models:/app/models
      - ./logs:/app/logs
      - ./backups:/app/backups
    restart: unless-stopped
    networks:
      - aquesa-network

  # Test API Server (for development/testing)
  aquesa-test-api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: aquesa-test-api
    ports:
      - "8001:8000"
    environment:
      - ENVIRONMENT=testing
      - DEBUG=true
      - USE_MOCK_DATA=true
    volumes:
      - .:/app
    command: ["python", "test_api_server.py"]
    restart: unless-stopped
    networks:
      - aquesa-network
    profiles:
      - testing

networks:
  aquesa-network:
    driver: bridge
